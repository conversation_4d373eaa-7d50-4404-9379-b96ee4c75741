<template>
  <a-modal
    v-model:open="dialog.visible"
    title="新增基础组件"
    width="600px"
    centered
    :confirm-loading="loading"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form
      ref="formRef"
      :model="formState"
      :label-col="{ style: 'width: 100px' }"
      :wrapper-col="{ style: 'width: calc(100% - 100px)' }"
      layout="horizontal"
    >
      <a-form-item
        label="组件名称"
        name="componentName"
        :rules="[{ required: true, message: '请输入组件名称' }]"
      >
        <a-input
          v-model:value="formState.componentName"
          placeholder="请输入组件名称"
          allow-clear
          :maxlength="50"
          show-count
        />
      </a-form-item>

      <a-form-item
        label="组件图标"
        name="componentIcon"
        :rules="[{ required: true, message: '请选择组件图标' }]"
      >
        <IconMaker v-model:value="formState.componentIcon" />
      </a-form-item>

      <a-form-item label="组件描述" name="remarks">
        <a-textarea
          v-model:value="formState.remarks"
          placeholder="请输入组件描述（可选）"
          :rows="4"
          :maxlength="200"
          show-count
          allow-clear
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script lang="ts" setup>
import { ToolCreateParams } from '@/master/types/tool';
import { createTool } from '@/master/apis/tools';
import { useDialog } from 'dialog-async';
import IconMaker from 'shared/components/IconMaker/index.vue';
import { FormInstance, message } from 'ant-design-vue';
import { COMPONENT_TYPE_IDS } from '../constants';

// 对话框实例
const dialog = useDialog();
const formRef = useTemplateRef<FormInstance>('formRef');

// 加载状态
const loading = ref(false);

// 表单数据
const formState = ref<Partial<ToolCreateParams>>({
  sysDesComponentTypeId: COMPONENT_TYPE_IDS.BASIC, // 基础组件类型ID
  componentName: '',
  componentIcon: undefined,
  remarks: '',
});

// 事件处理函数
const handleCancel = () => {
  dialog.cancel();
};

const handleOk = async () => {
  try {
    loading.value = true;

    // 表单验证
    await formRef.value?.validateFields();

    // 提交数据
    const result = await createTool({
      ...formState.value,
      componentType: 1, // 基础组件类型
      enable: 1,
      bsflag: 'N',
    } as any);

    message.success('基础组件创建成功');
    dialog.submit(result);
  } catch (error: any) {
    if (error.errorFields) {
      message.error('请填写完整信息');
    } else {
      message.error(error.message || '创建失败');
    }
  } finally {
    loading.value = false;
  }
};
</script>
