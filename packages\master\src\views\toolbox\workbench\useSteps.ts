import ParamsConfig from './components/ParamsConfig/index.vue';
import PageDesign from './components/PageDesign/index.vue';
import Debug from './components/Debug/index.vue';
import Release from './components/Release/index.vue';

export default () => {
  const current = ref(0);

  const steps = [
    {
      title: '基础信息',
      key: 'release',
      component: Release,
    },
    {
      title: '出入参设置',
      key: 'paramsConfig',
      component: ParamsConfig,
    },
    {
      title: '参数绑定',
      key: 'pageDesign',
      component: PageDesign,
    },
    {
      title: '调试运行',
      key: 'debug',
      component: Debug,
    },
  ];

  const next = () => {
    if (current.value < steps.length - 1) {
      current.value = current.value + 1;
    }
  };
  const prev = () => {
    if (current.value > 0) {
      current.value = current.value - 1;
    }
  };

  const currentComponent = computed(() => steps[current.value].component);

  const isLast = computed(() => current.value === steps.length - 1);

  return {
    current,
    currentComponent,
    steps,
    next,
    prev,
    isLast,
  };
};
