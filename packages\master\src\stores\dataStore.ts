import { getModelSoftwareList, queryByPage } from '@/master/apis/model';
import { ModelRecord } from '@/master/types/model';
import { getToolListByPage } from '@/master/apis/tools';
import { getToolTypeList } from '@/master/apis/toolType';
import { message } from 'ant-design-vue';
import type { SoftWare } from '@/master/types/software';
import type { Tool } from '@/master/types/tool';
import type { ToolType } from '@/master/types/toolType';

const dataStore = defineStore('dataStore', {
  state: () => ({
    software: [] as SoftWare[], // 专业软件
    models: [] as ModelRecord[], // 模型
    toolTypeMap: new Map<ToolType, Tool[]>(), // 工具组件分类
  }),
  actions: {
    async initSoftware() {
      const res = await getModelSoftwareList();
      this.software = res || [];
    },
    getSoftwareById(id: string) {
      return this.software.find(item => item.desProfessionalSoftwareId === id);
    },
    async initModels(params: {
      data: {
        desProfessionalSoftwareId: string;
      };
      pageNum: number;
      pageSize: number;
    }) {
      const res = await queryByPage(params);
      this.models = res.records || [];
    },
    getModelById(id: string) {
      return this.models.find(item => item.sysDesModelId === id);
    },
    /**
     * 初始化所有工具类别和工具数据
     */
    async initTools(queryAll?: boolean) {
      try {
        this.toolTypeMap.clear();
        const res = await getToolTypeList({});
        for (let t of res.records) {
          await this.initComponentsByType({
            sysDesComponentTypeId: t.sysDesComponentTypeId,
            name: t.name,
            pageNum: 1,
            pageSize: 1000,
            componentStatus: queryAll ? undefined : '1',
          });
        }
      } catch (e: any) {
        message.error(e.message);
      }
    },
    /**
     * 根据类别ID刷新特定类别下的工具数据
     * @param typeId 工具类别ID
     */
    async refreshToolsByTypeId(typeId: string) {
      try {
        // 查找对应的工具类别
        const toolType = this.getToolTypeById(typeId);
        if (toolType) {
          // 重新获取该类别下的工具数据
          await this.initComponentsByType({
            sysDesComponentTypeId: toolType.sysDesComponentTypeId,
            name: toolType.name,
            pageNum: 1,
            pageSize: 1000,
            componentStatus: '1',
          });
        }
      } catch (e: any) {
        message.error(e.message);
      }
    },
    /**
     * 初始化指定类别的工具组件
     * @param data 类别数据
     */
    async initComponentsByType(data: any) {
      const res = await getToolListByPage(data);
      this.toolTypeMap.set(data, res.records);
    },
    /**
     * 根据类别ID获取工具类别信息
     * @param typeId 类别ID
     * @returns 工具类别信息
     */
    getToolTypeById(typeId: string) {
      return this.toolTypes.find(t => t.sysDesComponentTypeId === typeId);
    },
    /**
     * 批量刷新多个类别的工具数据
     * @param typeIds 类别ID数组
     */
    async refreshMultipleToolTypes(typeIds: string[]) {
      try {
        const promises = typeIds.map(typeId =>
          this.refreshToolsByTypeId(typeId)
        );
        await Promise.all(promises);
      } catch (e: any) {
        message.error(e.message);
      }
    },
  },
  getters: {
    toolTypes: state => [...state.toolTypeMap.keys()],
  },
  persist: {
    pick: ['software'],
    storage: localStorage,
  },
});

export default dataStore;
