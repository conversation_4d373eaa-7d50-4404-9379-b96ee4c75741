/**
 * @file composables/useToolbox.ts
 * @description 工具箱组合式函数
 */

import { h } from 'vue';

/**
 * 视图模式类型
 */
type ViewMode = 'table' | 'card';

/**
 * 工具箱主要逻辑
 */
export function useToolbox() {
  // 视图模式
  const viewMode = ref<ViewMode>('card');

  // 事件处理函数
  const handleViewModeChange = (mode: ViewMode) => {
    viewMode.value = mode;
  };

  return {
    // 状态
    viewMode,
    // 方法
    handleViewModeChange,
  };
}

/**
 * 工具操作相关逻辑
 */
export function useToolActions() {
  const handleAddSoftwareComponent = async () => {
    const { showDialog } = await import('dialog-async');
    const { default: AddSoftwareComponentModal } = await import(
      '../components/AddSoftwareComponentModal.vue'
    );
    await showDialog(h(AddSoftwareComponentModal));
    // 刷新工具列表数据
  };

  const handleAddBasicComponent = async () => {
    const { showDialog } = await import('dialog-async');
    const { default: AddBasicComponentModal } = await import(
      '../components/AddBasicComponentModal.vue'
    );
    try {
      await showDialog(h(AddBasicComponentModal));
      // 只刷新基础组件类别的工具数据
      const dataStore = (await import('@/master/stores/dataStore')).default();
      const basicTypeId = dataStore.toolTypes.find(
        t => t.name === '通用组件'
      )?.sysDesComponentTypeId;
      if (basicTypeId) {
        await dataStore.refreshToolsByTypeId(basicTypeId);
      }
      return true;
    } catch {
      return false;
    }
  };

  return {
    handleAddSoftwareComponent,
    handleAddBasicComponent,
  };
}
