import { request } from '@kd/utils';
import type {
  ProfessionalSoftware,
  ProfessionalSoftwareCreateDto,
  ProfessionalSoftwareUpdateDto,
  ProfessionalSoftwareQueryDto,
} from '../types/professional-software';

/**
 * 分页查询专业软件类别维护列表
 * @param data 查询参数
 * @returns 分页数据
 */
export const getProfessionalSoftwareListByPage = (
  data: Global.Pagination & ProfessionalSoftwareQueryDto
) =>
  request<Global.ListResponse<ProfessionalSoftware>>({
    url: `/component/sysDesProfessionalSoftware/listPage?pageNum=${data.pageNum}&pageSize=${data.pageSize}`,
    method: 'POST',
    data,
  });

/**
 * 查询专业软件类别维护列表
 * @param data 查询参数
 * @returns 列表数据
 */
export const getProfessionalSoftwareList = (
  data: ProfessionalSoftwareQueryDto
) =>
  request<ProfessionalSoftware[]>({
    url: '/component/sysDesProfessionalSoftware/selectList',
    method: 'POST',
    data,
  });

/**
 * 通过ID查询专业软件类别
 * @param id 软件类别ID
 * @returns 专业软件类别详情
 */
export const getProfessionalSoftwareById = (id: string) =>
  request<ProfessionalSoftware>({
    url: '/component/sysDesProfessionalSoftware/queryById',
    method: 'GET',
    params: { id },
  });

/**
 * 新增专业软件类别
 * @param data 专业软件类别数据
 * @returns 新增结果
 */
export const createProfessionalSoftware = (
  data: ProfessionalSoftwareCreateDto
) =>
  request<ProfessionalSoftware>({
    url: '/component/sysDesProfessionalSoftware/add',
    method: 'POST',
    data,
  });

/**
 * 更新专业软件类别
 * @param data 专业软件类别数据
 * @returns 更新结果
 */
export const updateProfessionalSoftware = (
  data: ProfessionalSoftwareUpdateDto
) =>
  request<boolean>({
    url: '/component/sysDesProfessionalSoftware/update',
    method: 'POST',
    data,
  });

/**
 * 删除专业软件类别
 * @param idList 专业软件类别ID列表
 * @returns 删除结果
 */
export const deleteProfessionalSoftware = (idList: string[]) =>
  request<number>({
    url: '/component/sysDesProfessionalSoftware/delete',
    method: 'GET',
    params: { idList },
  });
