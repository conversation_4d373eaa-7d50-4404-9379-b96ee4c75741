export interface ProfessionalSoftwareBase {
  /**
   * 软件软件类别ID
   */
  desProfessionalSoftwareId: string;
  /**
   * 专业软件名称
   */
  softwareName: string;
  /**
   * 描述
   */
  softwareDesc: string;
  /**
   * 安装目录
   */
  workDir: string | null;
  /**
   * 软件域
   */
  softwareDomain: string | null;
  /**
   * 软件类别编码
   */
  softwareCode: string;
}

export interface ProfessionalSoftware extends Global.Entity {}

export type ProfessionalSoftwareCreateDto = Omit<
  ProfessionalSoftwareBase,
  'desProfessionalSoftwareId'
>;
export type ProfessionalSoftwareUpdateDto = Partial<ProfessionalSoftwareBase>;
export type ProfessionalSoftwareQueryDto = Partial<ProfessionalSoftwareBase>;
