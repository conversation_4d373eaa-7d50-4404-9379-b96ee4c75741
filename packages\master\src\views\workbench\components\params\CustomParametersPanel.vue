<template>
  <section
    class="parameters-panel"
    :class="{ 'parameters-panel--collapsed': !isPanelExpanded }"
  >
    <fieldset class="parameters-panel__title">
      <div class="modal-title-with-legend">
        <span>参数列表</span>
        <div class="parameter-legend">
          <div class="legend-item">
            <component :is="getIcon('INPUT')" class="text-blue-400" />
            <span>输入参数</span>
          </div>
          <div class="legend-item">
            <component :is="getIcon('OUTPUT')" class="text-orange-400" />
            <span>输出参数</span>
          </div>
        </div>
      </div>
    </fieldset>
    <div class="parameters-panel__toggle" @click="handleTogglePanel">
      <DoubleLeftOutlined v-if="!isPanelExpanded" />
      <DoubleRightOutlined v-else />
    </div>
    <div class="parameters-panel__container">
      <div class="parameters-panel__content">
        <div
          v-for="group in parameterGroups"
          :key="group.key"
          class="parameters-panel__group"
        >
          <p class="parameters-panel__group-title">
            <component :is="getIcon(group.value.toUpperCase())" />
            <span class="ml-2 font-bold text-[#777]">{{ group.title }}</span>
          </p>
          <div
            v-for="parameter in group.children"
            :key="parameter.key"
            class="parameters-panel__item"
          >
            <component
              :is="getIcon(parameter.type === 'in' ? 'INPUT' : 'OUTPUT')"
              :class="
                parameter.type === 'in' ? 'text-blue-400' : 'text-orange-400'
              "
            />
            <span class="whitespace-nowrap">{{ parameter.title }}</span>
            <span class="parameters-panel__item-value">
              {{
                isNaN(Number(parameter.value))
                  ? parameter.value
                  : Number(parameter.value).toFixed(4)
              }}
            </span>
            <span class="parameters-panel__item-unit">
              {{ parameter.unit }}
            </span>
          </div>
        </div>
        <a-empty v-if="!hasParameters"></a-empty>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
/**
 * @file CustomParametersPanel.vue
 * @description 工作流参数面板组件，用于展示工作流节点的参数信息
 */
import DoubleLeftOutlined from '~icons/ant-design/double-left-outlined';
import DoubleRightOutlined from '~icons/ant-design/double-right-outlined';
import eventEmitter from 'shared/utils/eventEmitter';
import type { TreeProps } from 'ant-design-vue';
import { getIcon } from '@/master/views/toolbox/icons';

/**
 * 参数组数据
 */
const parameterGroups = ref<TreeProps['treeData']>([]);

/**
 * 面板展开状态
 */
const isPanelExpanded = ref(true);

/**
 * 是否有参数数据
 */
const hasParameters = computed(
  () => parameterGroups.value && parameterGroups.value.length > 0
);

/**
 * 切换面板展开/折叠状态
 */
const handleTogglePanel = (): void => {
  isPanelExpanded.value = !isPanelExpanded.value;
};

/**
 * 监听节点变化事件，更新参数数据
 */
onMounted(() => {
  eventEmitter.on('node:change', (data: TreeProps['treeData']) => {
    parameterGroups.value = data;
  });
});
</script>

<style lang="less">
.parameters-panel {
  border: 1px solid #ccc;
  box-shadow: 0 0 5px #ccc;
  border-radius: 8px;
  box-sizing: border-box;
  background: white;
  position: relative;
  transition: width 0.3s ease;
  height: calc(100vh - 80px);

  &--collapsed {
    transition: transform 0.3s ease-in-out;
    transform: translateX(100%);
  }

  &__title {
    padding: 12px;
    font-weight: bold;
    color: #666;
    border-bottom: 1px solid #dedede;
  }

  &__toggle {
    position: absolute;
    width: 15px;
    background: rgba(0, 0, 0, 0.1);
    color: white;
    left: -17px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    padding: 20px 0;
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;

    &:hover {
      background: rgba(0, 0, 0, 0.15);
    }
  }

  &__container {
    height: calc(100% - 45px);
    overflow: auto;
  }

  &__content {
    padding: 8px;
  }

  &__group {
    padding: 8px 0;

    &-title {
      color: var(--text-100);
      font-weight: bold;
      display: flex;
      align-items: center;
    }
  }

  &__item {
    padding: 4px 0 0 20px;
    display: flex;
    align-items: center;

    &-value {
      margin-left: 4px;
      font-weight: 500;
      color: var(--primary-color);
    }
    &-unit {
      margin-left: 4px;
      color: var(--text-200);
    }
  }
}
/* 弹窗标题和图例样式 */
.modal-title-with-legend {
  display: flex;
  align-items: center;
}

.parameter-legend {
  display: flex;
  align-items: center;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: normal;
  margin-left: 8px;
  color: #595959;
}
</style>
