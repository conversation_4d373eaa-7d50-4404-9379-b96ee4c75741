<template>
  <a-modal
    v-model:open="dialog.visible"
    title="选择专业软件类型"
    width="800px"
    :footer="null"
    centered
    @cancel="handleCancel"
  >
    <div class="software-selection-container">
      <div v-if="loading" class="loading-container">
        <a-spin size="large" tip="加载中..." />
      </div>

      <div v-else-if="!dataStore.software.length" class="empty-container">
        <a-empty description="暂无可用的专业软件" />
      </div>

      <div v-else class="software-grid">
        <div
          v-for="item in dataStore.software"
          :key="item.softwareCode"
          class="software-card"
          @click="handleSoftwareClick(item)"
        >
          <div class="software-icon">
            <component
              v-if="getIcon(item.softwareName)"
              :is="getIcon(item.softwareName)"
              class="icon-component"
            />
            <div v-else class="default-icon">
              {{ item.softwareName.charAt(0).toUpperCase() }}
            </div>
          </div>

          <div class="software-info">
            <h4 class="software-name">{{ item.softwareName }}</h4>
            <p class="software-desc">
              {{ item.softwareDesc || '专业软件组件' }}
            </p>
          </div>

          <div class="software-actions">
            <a-button type="primary" size="small"> 选择 </a-button>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>
<script setup lang="ts">
import { useDialog } from 'dialog-async';
import { getIcon } from '../icons';
import { SoftWare } from '@/master/types/software';
import useDataStore from '@/master/stores/dataStore';
// Store 和路由
const dataStore = useDataStore();
const router = useRouter();
const dialog = useDialog();

// 加载状态
const loading = ref(false);

// 事件处理函数
const handleCancel = () => {
  dialog.cancel();
};

const handleSoftwareClick = (item: SoftWare) => {
  dialog.cancel();
  router.push({
    path: '/toolbox/workbench',
    query: {
      professionalSoftwareId: item.desProfessionalSoftwareId,
    },
  });
};

// 初始化数据
const initData = async () => {
  if (dataStore.software.length === 0) {
    try {
      loading.value = true;
      await dataStore.initSoftware();
    } catch (error) {
      console.error('Failed to load software list:', error);
    } finally {
      loading.value = false;
    }
  }
};

onMounted(() => {
  initData();
});
</script>

<style lang="less" scoped>
.software-selection-container {
  min-height: 300px;

  .loading-container,
  .empty-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
  }

  .software-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
    padding: 16px 0;
  }

  .software-card {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fff;

    &:hover {
      border-color: var(--primary-color);
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
      transform: translateY(-2px);
    }

    .software-icon {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 60px;
      margin-bottom: 12px;

      .icon-component {
        font-size: 48px;
        color: var(--primary-color);
      }

      .default-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: var(--primary-color);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        font-weight: bold;
      }
    }

    .software-info {
      text-align: center;
      margin-bottom: 12px;

      .software-name {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
      }

      .software-desc {
        margin: 0;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
        line-height: 1.4;
      }
    }

    .software-actions {
      display: flex;
      justify-content: center;
    }
  }
}
</style>
