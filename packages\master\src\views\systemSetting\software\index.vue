<template>
  <div class="h-full">
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-5">
        <div>
          名称：
          <a-input
            v-model:value="search.name"
            placeholder="请输入名称"
            style="width: 220px"
          />
        </div>
        <a-button type="primary" @click="getTableData">搜索</a-button>
      </div>
      <a-button type="primary" @click="handleAdd">新建</a-button>
    </div>

    <div class="p-2">
      <a-table
        :data-source="tableData"
        :columns
        :scroll="{ y: tableHeight }"
        :pagination="{
          current: page.current,
          pageSize: page.size,
          total: page.total,
          onChange: (current, size) => {
            page.current = current;
            page.size = size;
          },
        }"
        size="small"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { showDialog } from 'dialog-async';
import { TableColumnsType } from 'ant-design-vue';
import FormModal from './FormModal.vue';

const search = ref({
  name: '',
  status: '1',
});

const tableData = ref(
  Array.from({ length: 20 }, (_, i) => ({
    name: 'name' + i,
    status: 'status' + i,
    createTime: new Date().toLocaleString(),
  }))
);
const columns: TableColumnsType<any> = [
  {
    title: '序号',
    key: 'index',
    width: 60,
    customRender: ({ index }) => index + 1,
  },
  { title: '专业软件名称', dataIndex: 'name' },
  { title: '状态', dataIndex: 'status' },
  { title: '创建时间', dataIndex: 'createTime' },
  {
    title: '操作',
    key: 'action',
    width: 180,
    customRender: ({ record }: { record: any }) => (
      <div class="flex">
        <a-button type="link" onClick={() => handleEdit(record)}>
          编辑
        </a-button>
        <a-popconfirm
          title="确定删除吗？"
          ok-text="确定"
          cancel-text="取消"
          onConfirm={() => handleDel(record)}
        >
          <a-button type="link">删除</a-button>
        </a-popconfirm>
      </div>
    ),
  },
];

const tableHeight = ref(window.innerHeight - 210);

const page = ref({
  current: 1,
  size: 10,
  total: tableData.value.length,
});

const getTableData = () => {
  console.log('get data  :>>', search.value, page.value);
};

const handleAdd = async () => {
  // router.push('/mywork');
  await showDialog(h(<FormModal isNew={true} />));
};

const handleEdit = (row: any) => {
  console.log('edit  :>>', row);
};

const handleDel = (row: any) => {
  console.log('del  :>>', row);
};

onMounted(() => {
  window.addEventListener('resize', () => {
    tableHeight.value = window.innerHeight - 210;
  });
});
</script>
