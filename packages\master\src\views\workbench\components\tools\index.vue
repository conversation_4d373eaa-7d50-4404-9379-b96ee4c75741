<template>
  <div
    class="tool-panel"
    :class="{ 'tool-panel--collapsed': !isPanelExpanded }"
  >
    <div class="tool-panel__toggle" @click="handleTogglePanel">
      <DoubleLeftOutlined v-if="isPanelExpanded" />
      <DoubleRightOutlined v-else />
    </div>
    <a-tabs v-model:active-key="activeTabKey" centered>
      <a-tab-pane key="tools" tab="工具库">
        <a-spin :spinning="isLoading">
          <div class="tool-panel__content">
            <a-collapse
              v-model:active-key="activeToolCategories"
              ghost
              :bordered="false"
            >
              <a-collapse-panel
                v-for="toolCategory in dataStore.toolTypes"
                :key="toolCategory.sysDesComponentTypeId"
              >
                <template #header>
                  <span class="tool-panel__category-title">{{
                    toolCategory.name
                  }}</span>
                </template>
                <div class="tool-panel__items-grid">
                  <ToolItem
                    v-for="tool in dataStore.toolTypeMap.get(toolCategory)"
                    :key="tool.sysDesComponentId"
                    :item="tool"
                  />
                </div>
              </a-collapse-panel>
            </a-collapse>
          </div>
        </a-spin>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup lang="ts">
/**
 * @file CustomToolPanel.vue
 * @description 工作流工具面板组件，用于展示和管理可用的工具组件
 */
import ToolItem from './ToolItem.vue';
import DoubleLeftOutlined from '~icons/ant-design/double-left-outlined';
import DoubleRightOutlined from '~icons/ant-design/double-right-outlined';
import useDataStore from '@/master/stores/dataStore';

/**
 * 当前激活的标签页
 */
const activeTabKey = ref('tools');

/**
 * 当前展开的工具类别
 */
const activeToolCategories = ref<string[]>([]);

/**
 * 工作流存储
 */
const dataStore = useDataStore();

/**
 * 加载状态
 */
const isLoading = ref(false);

/**
 * 面板展开状态
 */
const isPanelExpanded = ref(true);

/**
 * 切换面板展开/折叠状态
 */
const handleTogglePanel = (): void => {
  isPanelExpanded.value = !isPanelExpanded.value;
};
/**
 * 初始化工具列表
 */
const initializeTools = async (): Promise<void> => {
  try {
    isLoading.value = true;
    await dataStore.initTools();

    // 默认展开所有工具类别
    activeToolCategories.value = dataStore.toolTypes.map(
      toolCategory => toolCategory.sysDesComponentTypeId
    );
  } catch (error) {
    console.error('Failed to initialize tools:', error);
  } finally {
    isLoading.value = false;
  }
};

/**
 * 组件挂载时初始化工具
 */
onMounted(() => {
  if (dataStore.toolTypes.length === 0) {
    initializeTools();
  }
  // 默认展开所有工具类别
  activeToolCategories.value = dataStore.toolTypes.map(
    toolCategory => toolCategory.sysDesComponentTypeId
  );
});
</script>

<style lang="less" scoped>
.tool-panel {
  transition: transform 0.3s ease-in-out;
  border: 1px solid #ccc;
  box-shadow: 0 0 5px #ccc;
  border-radius: 8px;
  box-sizing: border-box;
  background: white;
  position: relative;
  z-index: 10;
  height: calc(100vh - 80px);

  &--collapsed {
    transform: translateX(-100%);
  }

  &__toggle {
    width: 15px;
    position: absolute;
    background: rgba(0, 0, 0, 0.1);
    color: white;
    right: -17px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    padding: 20px 0;
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;

    &:hover {
      background: rgba(0, 0, 0, 0.15);
    }
  }

  &__content {
    min-height: 200px;
    overflow: auto;
    height: calc(100vh - 144px);
  }

  &__category-title {
    font-weight: bold;
    color: #666;
  }

  &__items-grid {
    display: grid;
    text-align: center;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 8px;
    // max-height: 267px;
    // overflow-y: auto;
    // overflow-x: hidden;
  }
}
</style>
