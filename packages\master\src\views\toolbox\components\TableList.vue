<template>
  <div class="table-list-container">
    <!-- 搜索区域 -->
    <div class="search-section">
      <a-form layout="inline" class="search-form">
        <a-form-item label="组件名称">
          <a-input
            v-model:value="searchParams.componentName"
            placeholder="请输入组件名称"
            allow-clear
            @press-enter="handleSearch"
          />
        </a-form-item>
        <a-form-item label="组件状态">
          <a-select
            v-model:value="searchParams.componentStatus"
            placeholder="请选择状态"
            allow-clear
            style="width: 120px"
          >
            <a-select-option :value="ToolStatus.PUBLISHED">
              已发布
            </a-select-option>
            <a-select-option :value="ToolStatus.WAIT_APPROVAL">
              未发布
            </a-select-option>
            <a-select-option :value="ToolStatus.DRAFT">草稿</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">
              <SearchOutlined />
              搜索
            </a-button>
            <a-button @click="handleReset">
              <ReloadOutlined />
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <a-table
        bordered
        :data-source="data?.records"
        :columns="tableColumns"
        :scroll="{ y: tableHeight, x: 1400 }"
        :loading
        size="small"
        :pagination="false"
        row-key="sysDesComponentId"
      />
    </div>

    <!-- 分页区域 -->
    <div class="pagination-section">
      <a-pagination
        v-model:current="pagination.pageNum"
        v-model:pageSize="pagination.pageSize"
        show-size-changer
        show-quick-jumper
        :total="pagination.total"
        :show-total="(total: number) => `共 ${total} 条记录`"
        @change="handlePageChange"
        @show-size-change="handlePageSizeChange"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { message, TableColumnsType } from 'ant-design-vue';
import {
  getToolListByPage,
  updateToolStatus,
  updateTool,
} from '@/master/apis/tools';
import { showDialog } from 'dialog-async';
import dayjs from 'dayjs';
import AddBasicComponentModal from './AddBasicComponentModal.vue';
import { useRequest } from 'vue-hooks-plus';
import { Tool } from '@/master/types/tool';
import { ToolStatus, ToolType } from '../workbench/constants';
import {
  getStatusConfig,
  getAvailableActions,
  getNextStatus,
} from '../utils/statusUtils';
import SearchOutlined from '~icons/ant-design/search-outlined';
import ReloadOutlined from '~icons/ant-design/reload-outlined';

const router = useRouter();

// 搜索参数
const searchParams = ref({
  componentName: '',
  componentStatus: undefined as string | undefined,
});

// 分页参数
const pagination = ref({
  total: 0,
  pageNum: 1,
  pageSize: 20,
});

// 表格高度
const tableHeight = computed(() => document.documentElement.clientHeight - 340);

const tableColumns: TableColumnsType<Tool> = [
  {
    title: '序号',
    key: 'index',
    width: 80,
    fixed: 'left',
    align: 'center',
    customRender: ({ index }: { index: number }) => index + 1,
  },
  {
    title: '组件名称',
    dataIndex: 'componentName',
    key: 'componentName',
    align: 'center',
    minWidth: 140,
    fixed: 'left',
  },
  {
    title: '组件状态',
    dataIndex: 'componentStatus',
    key: 'componentStatus',
    align: 'center',
    customRender: ({ record }) => {
      const { color, text } = getStatusConfig(record.componentStatus);
      return <a-tag color={color}>{text}</a-tag>;
    },
    minWidth: 100,
  },
  {
    title: '组件类型',
    dataIndex: 'componentType',
    key: 'componentType',
    align: 'center',
    minWidth: 100,
  },
  {
    title: '创建人',
    dataIndex: 'createUser',
    key: 'createUser',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    align: 'center',
    key: 'createTime',
    minWidth: 140,
    customRender: ({ value }) => dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    title: '更新人',
    dataIndex: 'updateUser',
    key: 'updateUser',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    align: 'center',
    key: 'updateTime',
    minWidth: 140,
    customRender: ({ value }) => dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    title: '操作',
    key: 'action',
    minWidth: 220,
    fixed: 'right',
    align: 'center',
    customRender: ({ record }) => {
      const actions = getAvailableActions(record.componentStatus);
      return (
        <a-space size="small">
          {actions.canEdit && (
            <a-button type="link" onClick={() => handleEdit(record)}>
              {{ default: () => '编辑' }}
            </a-button>
          )}
          {actions.canDelete && (
            <a-popconfirm
              title="确定删除吗？"
              okText="确定"
              cancelText="取消"
              onConfirm={() => handleDelete(record)}
            >
              <a-button type="link" danger>
                {{ default: () => '删除' }}
              </a-button>
            </a-popconfirm>
          )}
          {actions.canTogglePublish && (
            <a-button type="link" onClick={() => handleUpdateStatus(record)}>
              {{
                default: () => {
                  return record.componentStatus === ToolStatus.PUBLISHED
                    ? '取消发布'
                    : '发布';
                },
              }}
            </a-button>
          )}
        </a-space>
      );
    },
  },
];

// 数据请求
const { data, loading, run } = useRequest(getToolListByPage, {
  defaultParams: [{ ...pagination.value, ...searchParams.value }],
  onSuccess: res => {
    pagination.value = {
      total: res.total,
      pageSize: res.size,
      pageNum: res.current,
    };
  },
});

// 刷新数据
const refresh = () => {
  run({
    pageNum: pagination.value.pageNum,
    pageSize: pagination.value.pageSize,
    ...searchParams.value,
  });
};

// 搜索事件处理
const handleSearch = () => {
  pagination.value.pageNum = 1; // 重置到第一页
  refresh();
};

// 重置搜索
const handleReset = () => {
  searchParams.value = {
    componentName: '',
    componentStatus: undefined,
  };
  pagination.value.pageNum = 1;
  refresh();
};

// 分页变化处理
const handlePageChange = (page: number, pageSize: number) => {
  pagination.value.pageNum = page;
  pagination.value.pageSize = pageSize;
  refresh();
};

// 页面大小变化处理
const handlePageSizeChange = (_current: number, size: number) => {
  pagination.value.pageNum = 1; // 重置到第一页
  pagination.value.pageSize = size;
  refresh();
};

const handleEdit = async (row: Tool) => {
  if (row.componentType === ToolType.SOFT) {
    router.push({
      path: '/toolbox/workbench',
      query: {
        sysDesComponentId: row.sysDesComponentId,
        professionalSoftwareId: row.desProfessionalSoftwareId,
      },
    });
  } else {
    showDialog(<AddBasicComponentModal />).catch(() => {});
  }
};

const handleDelete = async (row: Tool) => {
  const res = await updateTool({
    sysDesComponentId: row.sysDesComponentId,
    bsflag: 'Y',
  });
  if (res) {
    message.success('删除成功');
    refresh();
  }
};

const handleUpdateStatus = async (record: Tool) => {
  try {
    const newStatus = getNextStatus(record.componentStatus);

    await updateToolStatus({
      sysDesComponentId: record.sysDesComponentId,
      status: newStatus,
    });
    message.success('操作成功');
    refresh();
  } catch (error) {
    message.error('操作失败');
  }
};
</script>

<style lang="less" scoped>
.table-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .search-section {
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
    margin-bottom: 16px;

    .search-form {
      margin: 0;

      .ant-form-item {
        margin-bottom: 0;
      }
    }
  }

  .table-section {
    flex: 1;
    overflow: hidden;
  }

  .pagination-section {
    padding: 16px 0;
    display: flex;
    justify-content: flex-end;
    border-top: 1px solid #f0f0f0;
  }
}
</style>
