/**
 * @file statusUtils.ts
 * @description 工具状态相关的工具函数
 */

import { ToolStatus } from '../workbench/constants';

/**
 * 获取状态显示文本
 * @param status 状态值
 * @returns 状态文本
 */
export const getStatusText = (status: string): string => {
  switch (status) {
    case ToolStatus.PUBLISHED:
      return '已发布';
    case ToolStatus.DRAFT:
      return '草稿';
    case ToolStatus.WAIT_APPROVAL:
    default:
      return '未发布';
  }
};

/**
 * 获取状态显示颜色
 * @param status 状态值
 * @returns 状态颜色
 */
export const getStatusColor = (status: string): string => {
  switch (status) {
    case ToolStatus.PUBLISHED:
      return 'success';
    case ToolStatus.DRAFT:
      return 'blue';
    case ToolStatus.WAIT_APPROVAL:
    default:
      return 'error';
  }
};

/**
 * 获取状态配置（文本和颜色）
 * @param status 状态值
 * @returns 状态配置对象
 */
export const getStatusConfig = (status: string) => {
  return {
    text: getStatusText(status),
    color: getStatusColor(status),
  };
};

/**
 * 获取状态操作按钮文本
 * @param status 状态值
 * @returns 按钮文本
 */
export const getStatusButtonText = (status: string): string => {
  switch (status) {
    case ToolStatus.PUBLISHED:
      return '取消发布';
    case ToolStatus.DRAFT:
      return '发布'; // 草稿状态可以发布
    case ToolStatus.WAIT_APPROVAL:
    default:
      return '发布';
  }
};

/**
 * 计算状态切换后的新状态
 * @param currentStatus 当前状态
 * @returns 新状态
 */
export const getNextStatus = (currentStatus: string): string => {
  switch (currentStatus) {
    case ToolStatus.PUBLISHED:
      // 已发布 -> 未发布
      return ToolStatus.WAIT_APPROVAL;
    case ToolStatus.WAIT_APPROVAL:
    default:
      // 草稿或未发布 -> 已发布
      return ToolStatus.PUBLISHED;
  }
};

/**
 * 判断是否可以发布/取消发布
 * @param status 状态值
 * @returns 是否可以发布操作
 */
export const canTogglePublish = (status: string): boolean => {
  // 草稿状态不能直接发布/取消发布，需要先编辑完成
  return status !== ToolStatus.DRAFT;
};

/**
 * 判断是否可以编辑
 * @param status 状态值
 * @returns 是否可以编辑
 */
export const canEdit = (status: string): boolean => {
  // 所有状态都可以编辑
  return true;
};

/**
 * 判断是否可以删除
 * @param status 状态值
 * @returns 是否可以删除
 */
export const canDelete = (status: string): boolean => {
  // 所有状态都可以删除
  return true;
};

/**
 * 获取可用的操作列表
 * @param status 状态值
 * @returns 可用操作列表
 */
export const getAvailableActions = (status: string) => {
  return {
    canEdit: canEdit(status),
    canDelete: canDelete(status),
    canTogglePublish: canTogglePublish(status),
  };
};
