import { ref } from 'vue';
import { debugTool, getDebugResult } from '@/master/apis/tools';
import type { DebugBinding } from './types';
import { useCommandAssembler } from './useCommandAssembler';
import useDataStore from '@/master/stores/dataStore';
import type {
  Command,
  Params,
  ParamsMap,
  ToolCreateParams,
} from '@/master/types/tool';

export function useDebugProcess(
  toolParams: Ref<ToolCreateParams>,
  emitComplete: (value: string) => void,
  debugLogFunctions: {
    addLog: (message: string) => void;
    logDebugStart: (bindings: DebugBinding[], inputParams: Params[]) => void;
    logSoftwareRunning: () => void;
    logDebugComplete: () => void;
  }
) {
  const { assembleCommands } = useCommandAssembler();
  const { addLog, logDebugStart, logSoftwareRunning, logDebugComplete } =
    debugLogFunctions;
  const dataStore = useDataStore();

  const intervalId = ref<number>();

  function startDebug(bindings: DebugBinding[]) {
    const { inputParams, outputParams, paramsMap } = toolParams.value;
    // Log debug start with input parameters
    logDebugStart(bindings, inputParams);

    // Convert bindings to JSON object
    const jsonObject = convertBindingsToObject(bindings);
    // Get software and validate
    const software = dataStore.getSoftwareById(
      toolParams.value.desProfessionalSoftwareId
    );

    if (!software) {
      throw new Error('找不到对应专业软件指令！');
    }

    // Get model
    const model = dataStore.getModelById(toolParams.value.sysDesModelId);

    if (!model) {
      throw new Error('找不到对应模型！');
    }

    // Assemble commands
    const commands = assembleCommands(
      software.commandList,
      inputParams,
      outputParams,
      bindings,
      model.modelPath
    );

    // Update tool params
    toolParams.value.commands = commands;

    // Prepare debug parameters
    const params = prepareDebugParams(
      inputParams,
      outputParams,
      commands,
      paramsMap,
      toolParams.value.sysDesModelId,
      software.softwareCode,
      jsonObject
    );

    // Start debug process
    debugTool(params).then(instanceId => {
      startPollingResults(instanceId, params, emitComplete);
    });
  }

  function convertBindingsToObject(
    bindings: DebugBinding[]
  ): Record<string, any> {
    return bindings.reduce(
      (result, current) => {
        result[current.id] = current.props.value;
        return result;
      },
      {} as Record<string, any>
    );
  }

  function prepareDebugParams(
    inputParams: Params[],
    outputParams: Params[],
    commands: Command[],
    paramsMap: ParamsMap[],
    modelId: string,
    softwareCode: string,
    jsonObject: Record<string, any>
  ) {
    const finalInputParams = inputParams.map(t => {
      const item = paramsMap.find(item => item.command === t.commandCode);
      return {
        ...t,
        name: item?.paramCode,
      };
    });
    return {
      sysDesComponent: {
        inputParams: JSON.stringify(finalInputParams),
        outputParams: JSON.stringify(outputParams),
        commands: JSON.stringify(commands),
        paramsMap: JSON.stringify(paramsMap),
        sysDesModelId: modelId,
        professionalSoftwareType: softwareCode,
      },
      jsonObject,
    };
  }

  function startPollingResults(
    instanceId: string,
    params: any,
    onComplete: (result: string) => void
  ) {
    const pollResults = () => {
      logSoftwareRunning();

      getDebugResult(instanceId, params)
        .then(result => {
          if (result) {
            onComplete(result);
            logDebugComplete();
            stopPolling();
          }
        })
        .catch(error => {
          console.error('获取调试结果失败:', error);
          addLog('获取调试结果失败: ' + error);
          stopPolling();
        });
    };

    // Initial poll
    pollResults();

    // Set up interval for polling
    intervalId.value = window.setInterval(pollResults, 4000);
  }

  function stopPolling() {
    if (intervalId.value) {
      clearInterval(intervalId.value);
      intervalId.value = undefined;
    }
  }

  return {
    startDebug,
    stopPolling,
  };
}
