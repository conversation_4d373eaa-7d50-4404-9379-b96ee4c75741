/**
 * @file constants/index.ts
 * @description 工具箱常量配置文件
 */

/**
 * 工具箱视图模式
 */
export const VIEW_MODES = {
  TABLE: 'table',
  CARD: 'card',
} as const;

/**
 * 工具卡片配置
 */
export const TOOL_CARD = {
  MIN_WIDTH: 100,
  HEIGHT: 100,
  GRID_HEIGHT: 300,
  ICON_SIZE: 56,
} as const;

/**
 * 组件类型ID
 */
export const COMPONENT_TYPE_IDS = {
  BASIC: 'bce0fe6d7bc62931fde6e4d6a187fb61', // 基础组件类型ID
  // 注意：专业软件组件的类型ID是动态的，根据不同的专业软件而变化
  // 因此不在这里定义固定的ID
} as const;

/**
 * 表格配置
 */
export const TABLE_CONFIG = {
  MIN_HEIGHT: 200,
  SCROLL_X: 1400,
} as const;

/**
 * 动画配置
 */
export const ANIMATION = {
  DURATION: 300,
  HOVER_SCALE: 1.1,
  HOVER_TRANSLATE_Y: -2,
} as const;

/**
 * 颜色配置
 */
export const COLORS = {
  PRIMARY: 'var(--primary-color)',
  SUCCESS: '#52c41a',
  ERROR: '#ff4d4f',
  WARNING: '#faad14',
  INFO: '#1890ff',
} as const;
