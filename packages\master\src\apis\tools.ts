import { request } from '@kd/utils';
import type { Tool, ToolCreateDto, ToolUpdateDto } from '../types/tool';

/**
 * 获取列表数据
 * @param params
 * @returns
 */
export const getToolListByPage = (
  data: Global.Pagination & {
    keywords?: string;
    sysDesComponentTypeId?: string;
  }
) =>
  request<Global.ListResponse<Tool>>({
    url: `/component/sysDesComponent/queryByPage?pageNum=${data.pageNum}&pageSize=${data.pageSize}`,
    method: 'POST',
    data,
  });

/**
 * 更新列表item
 * @param params
 * @returns
 */
export const updateTool = (data: Partial<ToolUpdateDto>) =>
  request<boolean>({
    url: '/component/sysDesComponent/update',
    method: 'POST',
    data,
  });

/**
 * 新增
 * @param params
 * @returns
 */
export const createTool = (data: ToolCreateDto) =>
  request({
    url: '/component/sysDesComponent/insert',
    method: 'POST',
    data,
  });

/**
 * 通过id查询
 * @param id
 * @returns
 */
export const queryToolById = (id: string) =>
  request<Tool>({
    url: '/component/sysDesComponent/queryById',
    method: 'GET',
    params: { id },
  });

/**
 * 组件发布/取消发布
 */
export const updateToolStatus = (data: Partial<ToolUpdateDto>) =>
  request<boolean>({
    url: `/component/sysDesComponent/updateStatus?sysDesComponentId=${data.sysDesComponentId}&status=${data.status}`,
    method: 'POST',
    data,
  });

/**
 * 调试
 * @param data
 * @returns
 */
export const debugTool = async (data: any) => {
  return request({
    url: '/component/sysDesComponent/debug',
    method: 'POST',
    data,
  });
};

/**
 * 调试结果
 * @param data
 * @returns
 */
export const getDebugResult = async (id: string, data: any) => {
  return request({
    url: `/component/sysDesComponent/debugResult?instanceId=${id}`,
    method: 'POST',
    data,
  });
};
