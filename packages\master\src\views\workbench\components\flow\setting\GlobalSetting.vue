<template>
  <a-form
    ref="formRef"
    :model="workflowParams"
    :label-col="{ style: 'width: 80px' }"
  >
    <a-form-item
      label="流程名称"
      name="wfName"
      :rules="{
        required: true,
        message: '请输入流程名称',
      }"
    >
      <a-input v-model:value="workflowParams.wfName" />
    </a-form-item>
    <a-form-item label="全局参数">
      <a-space
        v-for="(parameter, index) in workflowParams.globalData"
        :key="parameter.id"
        style="display: flex; margin-bottom: 8px"
        align="baseline"
      >
        <a-form-item
          label="参数名"
          :name="['globalData', index, 'paramKey']"
          :rules="{
            required: true,
            message: '请输入参数名',
          }"
        >
          <a-input v-model:value="parameter.paramKey" />
        </a-form-item>
        <a-form-item
          label="参数值"
          :name="['globalData', index, 'paramValue']"
          :rules="{
            required: true,
            message: '请输入参数值',
          }"
        >
          <a-input v-model:value="parameter.paramValue" />
        </a-form-item>
        <a-form-item label="单位" :name="['globalData', index, 'unit']">
          <a-input v-model:value="parameter.unit" />
        </a-form-item>
        <a-tooltip title="删除">
          <MinusCircleOutlined
            class="cursor-pointer"
            @click="removeParameter(parameter)"
          />
        </a-tooltip>
      </a-space>
      <a-button type="dashed" block @click="addParameter">
        <PlusOutlined />
        新增参数
      </a-button>
    </a-form-item>
    <a-form-item label="描述" name="wfDescription">
      <a-textarea
        v-model:value="workflowParams.wfDescription"
        :rows="3"
        placeholder="请输入描述"
      />
    </a-form-item>
  </a-form>
</template>
<script lang="ts" setup>
import PlusOutlined from '~icons/ant-design/plus-outlined';
import MinusCircleOutlined from '~icons/ant-design/minus-circle-outlined';
import type { FormInstance } from 'ant-design-vue';
import { useWorkflowGraph } from '../useWorkFlowGraph';

/**
 * 参数接口定义
 */
interface Parameter {
  /** 参数键 */
  paramKey: string;
  /** 参数值 */
  paramValue: string;
  unit: string;
  /** 唯一标识 */
  id: number;
}

const { workflowParams } = useWorkflowGraph();

/**
 * 表单引用
 */
const formRef = useTemplateRef<FormInstance>('formRef');

/**
 * 移除参数
 * @param {Parameter} parameter - 要移除的参数
 */
const removeParameter = (parameter: Parameter): void => {
  const parameters = workflowParams.value.globalData as Parameter[];
  const index = parameters.findIndex(item => parameter.id === item.id);
  if (index !== -1) {
    parameters.splice(index, 1);
  }
};

/**
 * 添加参数
 */
const addParameter = (): void => {
  const parameters = workflowParams.value.globalData as Parameter[];
  parameters.push({
    paramKey: '',
    paramValue: '',
    unit: '',
    id: Date.now(),
  });
};
</script>
