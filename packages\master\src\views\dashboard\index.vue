<template>
  <div class="h-full">
    <div class="flex items-center justify-between p-2">
      <a-form layout="inline">
        <a-form-item label="名称">
          <a-input v-model:value="search.name" placeholder="请输入名称" />
        </a-form-item>
        <a-form-item label="状态">
          <a-select
            v-model:value="search.status"
            :options="[
              { value: '1', label: '全部' },
              { value: '2', label: '已开始' },
              { value: '3', label: '未开始' },
            ]"
            style="width: 220px"
          />
        </a-form-item>
        <a-button type="primary" @click="refresh">搜索</a-button>
      </a-form>
      <a-button type="primary" @click="handleAdd">
        <template #icon>
          <GridiconsAdd />
        </template>
        新建
      </a-button>
    </div>

    <a-table
      :data-source="data"
      :columns="tableColumns"
      :scroll="{ x: 1200, y: tableHeight }"
      :pagination="false"
      bordered
      :loading
      size="small"
    />
    <div class="flex justify-end mt-2">
      <a-pagination
        v-model:current="pagination.current"
        v-model:pageSize="pagination.size"
        show-size-changer
        :total="pagination.total"
        @change="onShowSizeChange"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { Button, Space, TableColumnsType, message } from 'ant-design-vue';
import { delWorkflowById, queryByPage } from '@/master/apis/flow';
import GridiconsAdd from '~icons/gridicons/add';
import dayjs from 'dayjs';
import { useRequest } from 'vue-hooks-plus';

const search = ref({
  name: '',
  status: '1',
});

const tableHeight = computed(() => document.documentElement.clientHeight - 225);

const pagination = ref({
  current: 1,
  size: 20,
  total: 0,
});

const { data, loading, run } = useRequest(queryByPage, {
  defaultParams: [
    {
      pageNum: pagination.value.current,
      pageSize: pagination.value.size,
    },
  ],
  formatResult: res => {
    pagination.value.total = res.total;
    return res.records;
  },
});

const handleAdd = () => {
  router.push('/workbench');
};

const handleEdit = (row: any) => {
  router
    .push({
      path: '/workbench',
      query: { sysDesWorkflowId: row.sysDesWorkflowId },
    })
    .catch(() => {});
};

const handleDel = async (row: any) => {
  const res = await delWorkflowById([row.sysDesWorkflowId]);
  if (res.code === 200) {
    message.success('删除成功');
    refresh();
  }
};

const router = useRouter();

const handleJump = (row: any) => {
  router.push({
    path: '/system-setting/workflow-instance',
    query: { sysDesWorkflowId: row.powerjobWorkflowId },
  });
};

const tableColumns = computed<TableColumnsType>(() => [
  {
    title: '序号',
    key: 'index',
    width: 60,
    align: 'center',
    fixed: 'left',
    customRender: ({ index }) => index + 1,
  },
  {
    title: '工作流名称',
    dataIndex: 'wfName',
    align: 'center',
    minWidth: 140,
    fixed: 'left',
    customRender: ({ record }) => (
      <a-tooltip title={record.wfName}>
        <div class="whitespace-nowrap overflow-hidden text-ellipsis">
          {record.wfName}
        </div>
      </a-tooltip>
    ),
  },
  // { title: '工作流版本号', dataIndex: 'workflowVersion', align: 'center',minWidth: 140, },
  {
    title: '工作流描述',
    dataIndex: 'wfDescription',
    align: 'center',
    minWidth: 140,
  },
  {
    title: '创建人员',
    dataIndex: 'createUser',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '创建时间',
    align: 'center',
    dataIndex: 'createTime',
    minWidth: 140,
    customRender: ({ value }) => dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    title: '更新人员',
    align: 'center',
    dataIndex: 'updateUser',
    minWidth: 120,
  },
  {
    title: '更新时间',
    align: 'center',
    dataIndex: 'updateTime',
    minWidth: 140,
    customRender: ({ value }) => dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    title: '操作',
    key: 'action',
    minWidth: 200,
    align: 'center',
    fixed: 'right',
    customRender: ({ record }) => (
      <Space>
        <Button type="link" onClick={() => handleEdit(record)}>
          编辑
        </Button>
        <Button type="link" onClick={() => handleJump(record)}>
          实例
        </Button>
        <Button type="link" danger onClick={() => handleDel(record)}>
          删除
        </Button>
      </Space>
    ),
  },
]);

const refresh = () => {
  run({
    pageNum: pagination.value.current,
    pageSize: pagination.value.size,
  });
};

const onShowSizeChange = (current: number, pageSize: number) => {
  pagination.value.current = current;
  pagination.value.size = pageSize;
  refresh();
};

// socket.value = new WebSocket(`/hello`);

// socket.value.onmessage = (e: any) => {
//   console.log(e.data);
// };

// socket.value.onopen = () => {
//   console.log(123);
//   socket.value.send('111');
//   setTimeout(() => {
//     socket.value.close();
//   }, 2000);
// };
</script>
