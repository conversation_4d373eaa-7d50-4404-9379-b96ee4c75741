import {
  ParamType,
  MethodType,
  ToolStatus,
} from '../views/workbench/constants';

interface ToolBase {
  commands: string;
  componentIcon: string;
  componentName: string;
  componentOrder: number;
  componentStatus: ToolStatus;
  componentType: ToolType; // 1-开始；2-专业软件；9-结束
  desProfessionalSoftwareId: string;
  enable: 0 | 1;
  inputParams: string;
  outputParams: string;
  jobId?: number;
  nodeType: number; // 1-job任务，2-逻辑判断，3-子流程
  pageStructure: string;
  paramsMap: string;
  professionalSoftwareType?: string;
  remarks: string;
  sysDesComponentId: string;
  sysDesComponentTypeId: string;
  sysDesModelId: string;
  timeoutDuration: number;
}

export interface Tool extends ToolBase, Global.Entity {}

export interface ToolUpdateDto extends Tool {
  status: string;
}
export interface ToolCreateParams extends ToolBase {
  inputParams: Params[];
  outputParams: Params[];
  paramsMap: ParamsMap[];
  commands: Commands[];
  pageStructure: any[];
  bsflag: 'Y' | 'N';
  componentIcon?: {
    type: 'text' | 'image';
    content: string;
    backgroundColor?: string;
    color?: string;
  };
}
export interface ToolCreateDto extends ToolBase {
  enable?: 0 | 1;
  componentIcon?: string;
}

export interface Params {
  name: string;
  value?: string;
  unit: string;
  type: string;
  label: string;
  title: string;
  key: string;
  commandCode: string;
  defaultValue?: string;
}
export interface ParamsMap {
  paramType: ParamType;
  paramCode: string;
  command: string;
  dataFromType: string;
  dataFromCommand: string;
}
export interface Command {
  method: MethodType;
  name: string;
  unit: string;
  sortNo: number;
  isReturnToParam: boolean;
  value: string;
}
