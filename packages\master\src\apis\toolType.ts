import { request } from '@kd/utils';
import type { ToolType, ToolTypeParams } from '../types/toolType';

/**
 * 分页查询组件大类列表
 * @param params 查询参数
 * @returns 分页列表数据
 */
export function getToolTypeListByPage(params: ToolTypeParams) {
  return request<Global.ListResponse<ToolType>>({
    url: `/component/sysDesComponentType/listPage?current=${params.pageNum}&size=${params.pageSize}`,
    method: 'POST',
    data: {
      bsflag: params.bsflag,
      child: params.child,
    },
  });
}

/**
 * 查询组件大类列表（不分页）
 * @param params 查询参数
 * @returns 列表数据
 */
export function getToolTypeList(
  params: Omit<ToolTypeParams, 'pageNum' | 'pageSize'>
) {
  return request<Global.ListResponse<ToolType>>({
    url: '/component/sysDesComponentType/listType',
    method: 'POST',
    data: {
      bsflag: params.bsflag,
      child: params.child,
    },
  });
}

/**
 * 获取组件大类详情
 * @param id 组件大类ID
 * @returns 组件大类详情
 */
export function getToolTypeById(id: string) {
  return request<ToolType>({
    url: '/component/sysDesComponentType/select',
    method: 'GET',
    params: { id },
  });
}

/**
 * 新增组件大类
 * @param data 组件大类数据
 * @returns 新增结果
 */
export function createToolType(data: Partial<ToolType>) {
  return request<ToolType>({
    url: '/component/sysDesComponentType/add',
    method: 'POST',
    data,
  });
}

/**
 * 更新组件大类
 * @param data 组件大类数据
 * @returns 更新结果
 */
export function updateToolType(data: Partial<ToolType>) {
  return request<ToolType>({
    url: '/component/sysDesComponentType/update',
    method: 'POST',
    data,
  });
}

/**
 * 删除组件大类
 * @param ids 组件大类ID列表
 * @returns 删除结果
 */
export function deleteToolType(ids: string[]) {
  return request<boolean>({
    url: '/component/sysDesComponentType/delete',
    method: 'GET',
    params: { idList: ids },
  });
}
