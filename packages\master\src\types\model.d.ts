export interface ModelRecord extends Global.Entity {
  desProfessionalSoftwareId: string;
  modelName: string;
  parentId?: string;
  softwareCode: string;
  /** 模型版本 */
  modelVersion: string;
  /** 模型名称 */
  modelName: string;
  /** 模型路径 */
  modelPath: string;
  /** 模型ID */
  sysDesModelId: string;
  /** 模型截图 */
  modelImage: string;
  /** 备注 */
  remark: string;
}

export interface ModelData extends Global.Entity {
  commandCode: string;
  dataCode: string;
  dataName: string;
  dataUnit?: string;
  dataValue?: string;
  enumCode: string;
  parentId: string;
  sysDesModelDataId: string;
  sysDesModelId: string;
  unitEnum?: string;
  valueEnum?: string;
  parameterType: string;
}

/**
 * 模型存储服务器信息查询参数
 */
export interface SysDesModelServerQueryParams {
  /** 主键ID */
  sysDesModelServerId?: string;
  /** 专业软件主键ID */
  desProfessionalSoftwareId?: string;
  /** endpoint */
  endPoint?: string;
  /** accessKey */
  accessKey?: string;
  /** secretKey */
  secretKey?: string;
  /** bucketName */
  bucketName?: string;
  /** saveBasePath */
  saveBasePath?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 上传模型图片响应
 */
export interface UploadModelImageResponse {
  base64: string;
}
