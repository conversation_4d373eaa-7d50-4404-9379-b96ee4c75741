<template>
  <div class="card-view-container">
    <a-spin :spinning="loading">
      <div class="card-list-container">
        <div v-if="hasData" class="cards-grid">
          <Card
            v-for="category in dataStore.toolTypes"
            :key="category.sysDesComponentTypeId"
            :name="category.name"
            :children="dataStore.toolTypeMap.get(category)"
          />
        </div>
        <a-empty
          v-else
          description="暂无工具组件数据"
          class="empty-placeholder"
        >
        </a-empty>
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import useDataStore from '@/master/stores/dataStore';
import Card from './card.vue';

const dataStore = useDataStore();
const loading = ref(false);
// 计算属性
const hasData = computed(() => {
  return dataStore.toolTypeMap.size > 0;
});

const initTools = async () => {
  loading.value = true;
  try {
    await dataStore.initTools(true);
  } catch (error) {
    console.error('Failed to initialize tools:', error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  initTools();
});
</script>

<style lang="less">
.card-view-container {
  height: 100%;
  overflow-y: auto;
}
.card-list-container {
  height: 100%;

  .cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 16px;
    padding: 8px;
  }

  .empty-placeholder {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    min-height: 300px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .cards-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .cards-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}
</style>
