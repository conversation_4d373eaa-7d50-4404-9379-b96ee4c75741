/**
 * @file saveUtils.ts
 * @description 工具箱保存相关的工具函数
 */

import type { Params, ParamsMap, ToolCreateParams } from '@/master/types/tool';

// 常量定义
export const DEFAULT_COMPONENT_ORDER = 0;

/**
 * 映射参数名称
 * @param params 参数列表
 * @param paramsMap 参数映射表
 * @returns 映射后的参数列表
 */
export const mapParamsWithName = (
  params: Params[],
  paramsMap: ParamsMap[]
): Params[] => {
  return params.map(param => {
    const mappedItem = paramsMap.find(
      item => item.command === param.commandCode
    );
    return {
      ...param,
      name: mappedItem?.paramCode || param.name,
    };
  });
};

/**
 * 序列化工具参数
 * @param toolParams 工具参数
 * @param finalInputParams 最终输入参数
 * @param finalOutputParams 最终输出参数
 * @param professionalSoftwareId 专业软件ID
 * @returns 序列化后的参数对象
 */
export const serializeToolParams = (
  toolParams: ToolCreateParams,
  finalInputParams: Params[],
  finalOutputParams: Params[],
  professionalSoftwareId: string
) => {
  const { commands, pageStructure, paramsMap, componentIcon } = toolParams;

  return {
    ...toolParams,
    professionalSoftwareId,
    componentOrder: DEFAULT_COMPONENT_ORDER,
    pageStructure: JSON.stringify(pageStructure),
    desProfessionalSoftwareId: professionalSoftwareId,
    commands: JSON.stringify(commands),
    inputParams: JSON.stringify(finalInputParams),
    outputParams: JSON.stringify(finalOutputParams),
    paramsMap: JSON.stringify(paramsMap),
    componentIcon: componentIcon ? JSON.stringify(componentIcon) : undefined,
  };
};

/**
 * 验证组件
 * @param componentRef 组件引用
 * @returns 验证结果
 */
export const validateComponent = async (
  componentRef: any
): Promise<boolean> => {
  const validateFunc = componentRef?.validate;
  if (validateFunc && typeof validateFunc === 'function') {
    return await validateFunc();
  }
  return true;
};
