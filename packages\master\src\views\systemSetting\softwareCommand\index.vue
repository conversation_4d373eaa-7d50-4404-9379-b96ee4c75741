<template>
  <div class="h-full pt-4">
    <div class="bg-[#fff] rounded-lg">
      <div class="flex items-center justify-between p-2">
        <a-form :model="search" layout="inline">
          <a-form-item label="专业软件">
            <a-select
              v-model:value="desProfessionalSoftwareId"
              :options="softwares"
              style="width: 100px"
              @change="getTableData"
            />
          </a-form-item>
          <a-form-item label="中文名称">
            <a-input
              v-model:value="search.chineseName"
              placeholder="请输入中文名称"
            />
          </a-form-item>
          <a-form-item label="英文名称">
            <a-input
              v-model:value="search.englishName"
              placeholder="请输入英文名称"
            />
          </a-form-item>
          <a-form-item label="">
            <a-button type="primary" @click="getTableData">搜索</a-button>
          </a-form-item>
        </a-form>

        <a-button type="primary" @click="handleAdd">新建</a-button>
      </div>

      <div class="w-full p-2 flex flex-col">
        <a-table
          :data-source="tableData"
          :columns="columns"
          :loading="tableLoading"
          :style="{ height: tableHeight + 'px' }"
          :pagination="{
            current: page.current,
            pageSize: page.size,
            total: page.total,
            onChange: (current, size) => {
              page.current = current;
              page.size = size;
              getTableData();
            },
          }"
          bordered
          size="small"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { getModelSoftwareList } from '@/master/apis/model';
import { getCommandsList, DelCommandById } from '@/master/apis/commands';
import { showDialog } from 'dialog-async';
import CommandsModel from './FormModal.vue';
import { message } from 'ant-design-vue';
import { debounce } from 'radash';

const search = ref({
  chineseName: '',
  englishName: '',
});

const softwares = ref<{ label: string; value: string }[]>([]);

const desProfessionalSoftwareId = ref<string>('');

const tableData = ref([]);
const columns = [
  {
    title: '序号',
    key: 'index',
    width: 60,
    customRender: ({ index }: { index: number }) => index + 1,
  },
  { title: '命令名称', dataIndex: 'commandName' },
  { title: '中文名称', dataIndex: 'chineseName' },
  { title: '英文名称', dataIndex: 'commandName' },
  { title: '编码', dataIndex: 'commandCode' },
  { title: '操作类型', dataIndex: 'operateType' },
  { title: '创建人员', dataIndex: 'createUser' },
  { title: '创建时间', dataIndex: 'createTime' },
  { title: '更新人员', dataIndex: 'updateUser' },
  { title: '更新时间', dataIndex: 'updateTime' },
  {
    title: '操作',
    key: 'action',
    width: 180,
    customRender: ({ record }: { record: any }) => (
      <div class="flex">
        <a-button type="link" onClick={() => handleEdit(record)}>
          编辑
        </a-button>
        <a-popconfirm
          title="确定删除吗？"
          ok-text="确定"
          cancel-text="取消"
          onConfirm={() => handleDel(record)}
        >
          <a-button type="link">删除</a-button>
        </a-popconfirm>
      </div>
    ),
  },
];
const tableLoading = ref<boolean>(false);

const tableHeight = ref(window.innerHeight - 190);

const page = ref({
  current: 1,
  size: 10,
  total: tableData.value.length,
});

const getTableData = async () => {
  tableLoading.value = true;

  const params = {
    data: {
      desProfessionalSoftwareId: desProfessionalSoftwareId.value,
      chineseName: search.value.chineseName,
      englishName: search.value.englishName,
    },
    pageNum: page.value.current,
    pageSize: page.value.size,
  };
  const res: any = await getCommandsList(params);

  if (res && res.records.length > 0) {
    tableData.value = res.records;
    page.value.total = res.total;
  } else {
    tableData.value = [];
    page.value.total = 0;
  }
  tableLoading.value = false;
};

/**
 * 获取专业软件列表
 */
const getSoftwareList = async () => {
  const res = await getModelSoftwareList();
  if (res.length > 0) {
    softwares.value = res.map(
      (r: { softwareCode: string; desProfessionalSoftwareId: string }) => ({
        label: r.softwareCode,
        value: r.desProfessionalSoftwareId,
      })
    );

    desProfessionalSoftwareId.value = res[0].desProfessionalSoftwareId;
    getTableData();
  } else {
    softwares.value = [];
  }
};

const handleAdd = async () => {
  await showDialog(
    h(
      <CommandsModel
        isNew={true}
        modelId={desProfessionalSoftwareId.value}
        getList={getTableData}
      />
    )
  );
};

const handleEdit = async (record: any) => {
  await showDialog(
    h(
      <CommandsModel
        isNew={false}
        modelId={desProfessionalSoftwareId.value}
        command={record}
        getList={getTableData}
      />
    )
  );
};

const handleDel = debounce({ delay: 200 }, async (record: any) => {
  const res = await DelCommandById(record.sysDesCommandId);
  if (res) {
    message.success('删除成功');
    getTableData();
  }
});

onMounted(() => {
  getSoftwareList();

  window.addEventListener('resize', () => {
    tableHeight.value = window.innerHeight - 190;
  });
});
</script>
