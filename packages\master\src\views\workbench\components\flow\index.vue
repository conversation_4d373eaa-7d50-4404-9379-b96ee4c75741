<template>
  <div class="workflow-canvas">
    <div class="workflow-canvas__container">
      <div
        ref="canvasContainer"
        class="workflow-canvas__drop-area"
        @drop="handleDrop"
        @dragover="handleDragover"
      ></div>
      <TeleportContainer />
    </div>
    <div class="workflow-canvas__toolbar">
      <div class="cursor-pointer">
        <a-space size="large">
          <span>视图：</span>
          <span class="text-violet-500">工具流</span>
          <span
            class="ml-2 workflow-canvas__toolbar-link"
            @click="handleShowParamsView"
          >
            参数流
          </span>
          <a-divider type="vertical" />
          <span>工具：</span>
          <a-tooltip title="放大">
            <ZoomInOutlined
              class="workflow-canvas__toolbar-icon"
              @click="workflow?.zoomIn()"
            />
          </a-tooltip>
          <a-tooltip title="缩小">
            <ZoomOutOutlined
              class="workflow-canvas__toolbar-icon"
              @click="workflow?.zoomOut()"
            />
          </a-tooltip>
          <a-tooltip title="撤销">
            <ReloadOutlined
              class="workflow-canvas__toolbar-icon"
              @click="workflow?.undo()"
            />
          </a-tooltip>
          <a-tooltip title="居中">
            <UngroupOutlined
              class="workflow-canvas__toolbar-icon"
              @click="workflow?.resetView()"
            />
          </a-tooltip>
          <a-tooltip title="平移提示">
            <InfoCircleOutlined
              class="workflow-canvas__toolbar-icon"
              @click="showPanningTip"
            />
          </a-tooltip>
        </a-space>
      </div>
    </div>
    <NodeSetting />
  </div>
  <ParameterMapping ref="parameterMappingRef" />
</template>

<script setup lang="ts">
/**
 * @file CustomWorkflowCanvas.vue
 * @description 工作流画布组件，用于展示和编辑工作流图
 */
import NodeSetting from './setting/index.vue';
import ParameterMapping from './parameterMapping/index.vue';
import { useWorkflowGraph } from './useWorkFlowGraph';
import { getTeleport } from '@antv/x6-vue-shape';
import { generateUUID } from 'shared/utils';
import type { Tool } from '@/master/types/tool';
import { getDetailById } from '@/master/apis/flow';
import { message, Modal } from 'ant-design-vue';
import UngroupOutlined from '~icons/ant-design/ungroup-outlined';
import ZoomInOutlined from '~icons/ant-design/zoom-in-outlined';
import ZoomOutOutlined from '~icons/ant-design/zoom-out-outlined';
import InfoCircleOutlined from '~icons/ant-design/info-circle-outlined';
import ReloadOutlined from '~icons/ant-design/reload-outlined';
import {
  NODE_HEIGHT,
  GRAPH_SHAPE,
  NODE_TYPE,
  NODE_WIDTH,
  GROUP_NODE_WIDTH,
  GROUP_NODE_HEIGHT,
  CONDITION_NODE_HEIGHT,
} from '../../constants';
import { WorkflowGraph } from './core/WorkflowGraph';
import type { Node } from '@antv/x6';

/**
 * 画布容器引用
 */
const canvasContainer = useTemplateRef('canvasContainer');

/**
 * 路由实例
 */
const route = useRoute();

/**
 * 传送门容器组件
 */
const TeleportContainer = getTeleport();

/**
 * 工作流图实例
 */
const { workflow, initGrid, workflowParams } = useWorkflowGraph();

/**
 * 编辑模型引用
 */
const parameterMappingRef = ref();

/**
 * 显示参数视图
 */
const handleShowParamsView = (): void => {
  if (parameterMappingRef.value) {
    parameterMappingRef.value.visible = true;
  }
};

/**
 * 显示平移提示
 */
const showPanningTip = (): void => {
  Modal.info({
    title: '画布平移操作提示',
    content:
      '按住 Alt 键的同时拖动鼠标可以平移画布视图。您也可以使用工具栏中的放大、缩小和居中按钮来调整视图。',
    okText: '我知道了',
  });
};

/**
 * 处理拖拽悬停事件
 * @param {DragEvent} event - 拖拽事件对象
 */
const handleDragover = (event: DragEvent): void => {
  event.preventDefault();
};

const isGrouNode = (type: NODE_TYPE) =>
  [NODE_TYPE.GROUP, NODE_TYPE.LOOP].includes(type);

/**
 * 处理节点拖放
 * @param event - 拖放事件对象
 */
const handleDrop = async (event: DragEvent): Promise<void> => {
  try {
    const graph = workflow.value;
    if (!graph) {
      message.warning('画布未初始化');
      return;
    }

    // 获取拖拽数据
    const data = event.dataTransfer?.getData('application/json');
    if (!data) {
      message.warning('无效的拖拽数据');
      return;
    }

    const parsedData = JSON.parse(data) as Tool;

    // 验证开始和结束节点的唯一性
    if (!validateUniqueNode(graph, parsedData)) {
      return;
    }

    // 计算节点位置
    const position = calculateNodePosition(
      graph,
      event,
      parsedData.componentType
    );

    // 根据节点类型创建节点
    const newNode = createNode(graph, position, parsedData);

    // 处理节点放置到分组内
    handleNodeInGroup(graph, newNode, position);
  } catch (error: any) {
    message.error(error.message);
  }
};

/**
 * 验证开始和结束节点的唯一性
 */
const validateUniqueNode = (graph: WorkflowGraph, data: Tool): boolean => {
  const nodes = graph.getNodes();
  const hasStart = nodes.some(
    (node: Node) => node.getData().componentType === NODE_TYPE.START
  );
  const hasEnd = nodes.some(
    (node: Node) => node.getData().componentType === NODE_TYPE.END
  );
  const isStartNode = data.componentType === NODE_TYPE.START;
  const isEndNode = data.componentType === NODE_TYPE.END;

  if (isStartNode && hasStart) {
    message.warning('当前流程已经存在开始节点！');
    return false;
  }

  if (isEndNode && hasEnd) {
    message.warning('当前流程已经存在结束节点！');
    return false;
  }

  return true;
};

/**
 * 计算节点放置位置
 * @param graph - 工作流图实例
 * @param event - 拖拽事件
 * @param componentType - 节点类型
 */
const calculateNodePosition = (
  graph: WorkflowGraph,
  event: DragEvent,
  componentType: NODE_TYPE
) => {
  const width = isGrouNode(componentType) ? GROUP_NODE_WIDTH : NODE_WIDTH;
  const height = isGrouNode(componentType) ? GROUP_NODE_HEIGHT : NODE_HEIGHT;

  return graph.pageToLocal(
    event.clientX - width / 2,
    event.clientY - height / 2
  );
};

/**
 * 创建节点
 */
const createNode = (
  graph: WorkflowGraph,
  position: { x: number; y: number },
  data: Tool
): Node => {
  const { x, y } = position;

  // 判断是否为分组或循环节点
  if (isGrouNode(data.componentType)) {
    return graph.addGroupNode(x, y, {
      ...data,
      componentName: data.componentName,
    });
  }

  // 判断是否为条件节点
  if (data.componentType === NODE_TYPE.CONDITION) {
    return graph.addNode({
      id: generateUUID(),
      shape: GRAPH_SHAPE.CONDITION,
      x,
      y,
      width: GROUP_NODE_WIDTH,
      height: CONDITION_NODE_HEIGHT,
      data: {
        ...data,
        componentName: data.componentName,
        conditions: [],
      },
    });
  }

  // 添加普通节点
  return graph.addNode({
    id: generateUUID(),
    shape: GRAPH_SHAPE.NORMAL,
    x,
    y,
    width: NODE_WIDTH,
    height: NODE_HEIGHT,
    data,
  });
};

/**
 * 处理节点放置到分组内
 */
const handleNodeInGroup = (
  graph: WorkflowGraph,
  node: Node,
  position: { x: number; y: number }
): void => {
  // 创建节点的边界框
  const nodeBBox = {
    x: position.x,
    y: position.y,
    width: NODE_WIDTH,
    height: NODE_HEIGHT,
  };

  // 获取所有分组节点
  const groupNodes = graph.getNodes().filter((node: Node) => {
    return node.shape === GRAPH_SHAPE.GROUP;
  });

  // 查找目标分组节点
  for (const groupNode of groupNodes) {
    const groupBBox = groupNode.getBBox();

    // 检查节点是否在分组节点内部
    if (groupBBox.containsRect(nodeBBox)) {
      groupNode.addChild(node);
      graph.adjustGroupNodeSize(groupNode);
      break;
    }

    // 检查节点是否与分组节点有足够的交叉
    if (groupBBox.isIntersectWithRect(nodeBBox)) {
      const xOverlap = Math.max(
        0,
        Math.min(nodeBBox.x + nodeBBox.width, groupBBox.x + groupBBox.width) -
          Math.max(nodeBBox.x, groupBBox.x)
      );
      const yOverlap = Math.max(
        0,
        Math.min(nodeBBox.y + nodeBBox.height, groupBBox.y + groupBBox.height) -
          Math.max(nodeBBox.y, groupBBox.y)
      );
      const intersectionArea = xOverlap * yOverlap;
      const nodeArea = nodeBBox.width * nodeBBox.height;

      // 如果交叉面积超过节点面积的30%，则添加到分组节点中
      if (intersectionArea > nodeArea * 0.3) {
        groupNode.addChild(node);
        graph.adjustGroupNodeSize(groupNode);
        break;
      }
    }
  }
};

/**
 * 初始化工作流
 * @param {string} id - 工作流ID
 */
const initWorkflow = async (id: string): Promise<void> => {
  try {
    if (!workflow.value) return;

    const detail = await getDetailById(id);

    // 解析工作流数据
    workflowParams.value = {
      ...detail,
      globalData: JSON.parse(detail.globalData as string),
      globalDataMap: JSON.parse(detail.globalDataMap as string),
      nodeDataMap: JSON.parse(detail.nodeDataMap as string),
    };

    // 加载工作流图结构
    workflow.value?.fromJSON(JSON.parse(detail.pageStructure));
    workflow.value.getNodesParams();
    workflow.value.doLayout();
  } catch (error: any) {
    message.error(error.message);
  }
};

/**
 * 组件挂载时初始化画布
 */
onMounted(() => {
  if (canvasContainer.value) {
    // 初始化画布网格
    initGrid(canvasContainer.value);

    // 如果有工作流ID，则加载工作流
    const workflowId = route.query.sysDesWorkflowId as string;
    if (workflowId) {
      initWorkflow(workflowId);
    }
  }
});
</script>

<style lang="less">
.workflow-canvas {
  position: relative;
  &__container {
    width: 100%;
    height: 100%;
  }

  &__drop-area {
    width: 100%;
    height: 100%;
  }

  &__toolbar {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 20px;
    margin: 0 auto;
    background-color: white;
    box-shadow: 0 0 5px 0 #ccc;
    padding: 8px 30px;
    border-radius: 20px;
    color: rgb(68, 68, 68);

    &-link {
      cursor: pointer;

      &:hover {
        color: var(--primary-color);
      }
    }

    &-icon {
      cursor: pointer;

      &:hover {
        color: var(--primary-color);
      }
    }
  }
}
</style>
